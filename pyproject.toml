[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "code-standard-mcp"
version = "1.0.0"
description = "CodeStandardMCP - 编码规范服务工具，提供规范查询、合规检查和管理功能的MCP服务器"
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}
authors = [
    {name = "CodeStandardMCP", email = "<EMAIL>"},
]
keywords = ["mcp", "coding-standards", "compliance", "code-quality", "fastmcp", "async", "standards-as-service"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Code Generators",
    "Framework :: AsyncIO",
]
dependencies = [
    "httpx>=0.28.1",
    "fastmcp>=2.0.0",
    "pydantic>=2.0.0",
    "aiofiles>=23.0.0",
]



[project.urls]
Homepage = "https://github.com/example/code-standard-mcp"
Repository = "https://github.com/example/code-standard-mcp"
Issues = "https://github.com/example/code-standard-mcp/issues"
Documentation = "https://github.com/example/code-standard-mcp/blob/main/README.md"

[project.scripts]
code-standard-mcp = "main:main"

# UV 包管理器配置 - 使用中国镜像源
[tool.uv]
index-url = "https://pypi.tuna.tsinghua.edu.cn/simple"
extra-index-url = [
    "https://mirrors.aliyun.com/pypi/simple/",
    "https://pypi.douban.com/simple/"
]
