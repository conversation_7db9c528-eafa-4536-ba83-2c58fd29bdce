"""
CodeStandardMCP 配置管理模块

提供服务器配置、缓存设置、API配置等功能
"""

import logging
import os
from pathlib import Path
from typing import Any, Dict, Optional


class ServerConfig:
    """服务器配置管理类"""

    def __init__(self):
        # 基础配置
        self.name = "CodeStandardMCP"
        self.version = "1.0.0"
        self.description = "编码规范服务工具"

        # 网络配置
        self.http_timeout = float(os.getenv("CSMCP_HTTP_TIMEOUT", "30.0"))
        self.user_agent = os.getenv("CSMCP_USER_AGENT", "CodeStandardMCP/1.0 (httpx)")
        self.max_retries = int(os.getenv("CSMCP_MAX_RETRIES", "3"))

        # 缓存配置
        self.cache_ttl_hours = int(os.getenv("CSMCP_CACHE_TTL", "24"))
        self.max_cache_size_mb = int(os.getenv("CSMCP_MAX_CACHE_SIZE_MB", "100"))

        # 日志配置
        self.log_level = os.getenv("CSMCP_LOG_LEVEL", "INFO")
        self.log_file = os.getenv("CSMCP_LOG_FILE", None)
        self.mask_error_details = (
            os.getenv("CSMCP_MASK_ERROR_DETAILS", "false").lower() == "true"
        )

        # 存储配置
        self.data_dir = Path(os.getenv("CSMCP_DATA_DIR", "./data"))
        self.cache_dir = self.data_dir / "cache"

        # 确保目录存在
        self._ensure_directories()

        # API配置
        self.default_api_endpoints = {
            "python": "https://api.example.com/standards/python",
            "javascript": "https://api.example.com/standards/javascript",
            "java": "https://api.example.com/standards/java",
            "csharp": "https://api.example.com/standards/csharp",
            "go": "https://api.example.com/standards/go",
        }



    def _ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.data_dir, self.cache_dir]:
            directory.mkdir(parents=True, exist_ok=True)

    def get_cache_file_path(self, cache_key: str) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / f"{cache_key}.json"

    def get_api_endpoint(
        self, language: str, framework: Optional[str] = None
    ) -> Optional[str]:
        """获取指定语言和框架的API端点"""
        key = f"{language}_{framework}" if framework else language
        return self.default_api_endpoints.get(key.lower())

    def get_standard_file_path(
        self, language: str, framework: Optional[str] = None
    ) -> Path:
        """获取规范文件存储路径"""
        if framework:
            return self.standards_dir / language.lower() / f"{framework.lower()}.json"
        else:
            return self.standards_dir / language.lower() / "general.json"

    def get_cache_file_path(self, cache_key: str) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / f"{cache_key}.json"

    def get_index_file_path(self) -> Path:
        """获取索引文件路径"""
        return self.index_dir / "standards_index.json"

    def get_cache_registry_path(self) -> Path:
        """获取缓存注册表路径"""
        return self.index_dir / "cache_registry.json"

    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "http_timeout": self.http_timeout,
            "cache_duration_hours": self.cache_duration_hours,
            "max_cache_size_mb": self.max_cache_size_mb,
            "log_level": self.log_level,
            "data_dir": str(self.data_dir),
            "compliance_rules_enabled": self.compliance_rules_enabled,
            "max_code_length": self.max_code_length,
            "context_lines": self.context_lines,
        }


def setup_logging(config: ServerConfig) -> logging.Logger:
    """设置日志系统"""
    log_level = getattr(logging, config.log_level.upper(), logging.INFO)

    # 配置日志格式
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # 配置处理器
    handlers = []

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    handlers.append(console_handler)

    # 文件处理器（如果配置了日志文件）
    if config.log_file:
        file_handler = logging.FileHandler(config.log_file)
        file_handler.setFormatter(formatter)
        handlers.append(file_handler)

    # 配置根日志器
    logging.basicConfig(level=log_level, handlers=handlers, force=True)

    return logging.getLogger("CodeStandardMCP")


# 全局配置实例
config = ServerConfig()
logger = setup_logging(config)


def get_config() -> ServerConfig:
    """获取全局配置实例"""
    return config
